import 'package:wu_core/wu_extensions.dart';
import 'package:wu_fortune/wu_calendar.dart';

import '../wu_fortune.dart';
import 'gods.dart';

class Yijing {
  /// 六爻符號，左邊為上爻，-陽=陰o老陽x老陰
  /// 搖出得●◎◎，一背為單，畫─。得●●◎，二背為折，畫=。得●●●，三背為重，畫o。得◎◎◎，三面為交，畫x。
  late String _sign6;
  String get sign6 => _sign6;
  Yijing(String sign6) : _sign6 = sign6.toLowerCase();

  /// 梅花易數專用的三數起卦法，三數都從零開始
  factory Yijing.byNum3(int num1, int num2, int num3) {
    // print("[byNum3] num1: $num1, num2: $num2, num3: $num3");
    final signs = (Gua8.byIndex(num1).sign + Gua8.byIndex(num2).sign).split('');
    num3 = num3 % 6;
    if (signs[5 - num3] == "=") signs[5 - num3] = "x";
    if (signs[5 - num3] == "-") signs[5 - num3] = "o";
    return Yijing(signs.join());
  }

  /// 由卦名取得卦象
  factory Yijing.byGuaName(String orginal, String changed) {
    final orginalSign6 = Gua64.byName(orginal).sign6;
    final changedSign6 = Gua64.byName(changed).sign6;
    final sign6 = orginalSign6.split("");
    for (var i = 0; i < 6; i++) {
      if (orginalSign6[i] != changedSign6[i]) {
        sign6[i] = (orginalSign6[i] == "=") ? "o" : "x";
      }
    }
    return Yijing(sign6.join());
  }

  /// 卦名稱
  String get name {
    final org = Gua64.bySign6(orginal);
    final chg = Gua64.bySign6(changed);
    if (org.name == chg.name) {
      return org.fullname;
    } else {
      return "${org.xiang}之${chg.xiang}卦";
    }
  }

  /// 動爻數量
  int get dongCount => _sign6.split("").where((e) => e == "o" || e == "x").length;

  /// 動爻名稱(從初爻開始第一個動搖)
  String get dongYao {
    final rev = _sign6.reverse();
    final numstr = ["一", "二", "三", "四", "五", "六"];
    return numstr[rev.contains("o") ? rev.indexOf("o") : rev.indexOf("x")];
  }

  /// 原卦：代表的是目前的情況。
  String get orginal {
    var ret = _sign6;
    ret = ret.replaceAll("o", "-");
    ret = ret.replaceAll("x", "=");
    return ret;
  }

  /// 變卦：事情發展的結果。
  String get changed {
    var ret = _sign6;
    ret = ret.replaceAll("o", "=");
    ret = ret.replaceAll("x", "-");
    return ret;
  }

  /// **互卦** 表示提問事情發展的過程
  /// 將本卦中的第三、四、五爻，拿出來作為互卦的上卦，而將本卦中的第二、三、四爻
  String get interaction {
    // 乾坤無互，改用變卦
    var ret = (["------", "======"].contains(orginal)) ? changed : orginal;
    // 取互卦公式，互卦取本卦432爲下卦，543爲上卦
    ret = ret.substring(1, 4) + ret.substring(2, 5);
    return ret;
  }

  /// **錯卦** 從問題的對立面來分析問題。
  /// 將本卦各爻位置上的陰陽互換
  String get swap {
    var ret = orginal.split("");
    for (var i = 0; i < _sign6.length; i++) {
      ret[i] = ret[i] == "-" ? "=" : "-";
    }
    return ret.join("");
  }

  /// ## **綜卦** 從其他的角度分析問題。
  /// 將本卦各爻位置順序，上下顛倒重置(如：將初、二、三、四、五、上爻位置顛倒，重新排列而成上、五、四、三、二、初爻)，但陰陽不變，所得出之卦，就稱之為本卦的綜卦。
  String get reverse {
    var ret = orginal.split("").reversed.join();
    return ret;
  }

  /// 取得梅花易數的排盤
  Map<String, dynamic> getMeiyiLayout(GanZhi m) {
    if (dongCount != 1) throw Exception("梅花易數只能用於動爻數量為1的卦象");
    // 找到動爻的位置
    final dongIndex = _sign6.contains("o") ? _sign6.indexOf("o") : _sign6.indexOf("x");
    // 判斷動爻在上卦還是下卦
    final yongGua = dongIndex < 3 ? 0 : 1;
    final gua64s = [
      Gua64.bySign6(orginal),
      Gua64.bySign6(interaction),
      Gua64.bySign6(changed),
    ];
    // 按照體用規則，第一個是本卦的體卦，第二個是本卦的用卦，第三個是互卦的體卦，第四個是互卦的用卦，第五個是變卦的用卦
    final gua8s = <Map<String, dynamic>>[
      {"八卦": gua64s[0].gua8s[1 - yongGua]},
      {"八卦": gua64s[0].gua8s[yongGua]},
      {"八卦": gua64s[1].gua8s[0]},
      {"八卦": gua64s[1].gua8s[1]},
      {"八卦": gua64s[2].gua8s[yongGua]},
    ];
    // 體用規則是將第一個以外的卦對比體卦的五行判斷吉凶
    for (var i = 0; i < gua8s.length; i++) {
      gua8s[i]["卦序"] = gua8s[i]["八卦"]?.index;
      gua8s[i]["卦名"] = gua8s[i]["八卦"]?.name;
      gua8s[i]["五行"] = gua8s[i]["八卦"]?.wuxing.name;
      if (i == 0) continue;
      final sk = ShengKe.byCompare(gua8s[i]["八卦"], gua8s[0]["八卦"]);
      gua8s[i]["生剋"] = sk.name;
      gua8s[i]["吉凶"] = sk.jixiong;
      gua8s[i]["分數"] = sk.score;
      final skm = ShengKe.byCompare(m.zhi, gua8s[0]["八卦"]);
      gua8s[i]["旺衰"] = skm.wangxiang;
    }

    final result = <String, dynamic>{
      "用卦": yongGua,
      "八卦": gua64s,
      "單卦": gua8s,
    };
    return result;
  }

  Map<String, dynamic> getLiuyaoLayout(GanZhi m, GanZhi d) {
    final result = <String, dynamic>{
      "本卦": Gua64.bySign6(orginal),
      "變卦": Gua64.bySign6(changed),
    };
    final gong = result["本卦"]!.gong;
    result["伏卦"] = Gua64.byName(gong + gong);

    // 找世爻
    var shiyaoIndex = {
      "八純": 5, "一世": 0, "二世": 1, "三世": 2, //
      "四世": 3, "五世": 4, "遊魂": 3, "歸魂": 2, //
    };
    final yaoNames = "上五四三二初";
    final shiIndex = shiyaoIndex[result["本卦"]!.dai]!;
    result["世爻"] = yaoNames[5 - shiIndex];
    result["應爻"] = yaoNames[(5 - shiIndex + 3) % 6];
    result["世爻序"] = shiIndex;

    // 安六獸
    final shouList = "青朱勾蛇白玄" * 2;
    int getShouIndex() {
      final ganidx = [0, 0, 1, 1, 2, 3, 4, 4, 5, 5];
      final gan = d.gan;
      return ganidx[gan.index];
    }

    // 安六親
    String getQin(String zhi) {
      final gong = result["伏卦"].gua8s[0];
      final sk = ShengKe.byCompare(Zhi.byName(zhi), gong);
      return sk.liuqin;
    }

    final shouIndex = getShouIndex();
    final yaoList = <Map<String, dynamic>>[];
    final qinCollect = <String>{};
    for (var i = 0; i < 6; i++) {
      final yao = {
        "爻名": yaoNames[i],
        "六獸": shouList[shouIndex + i],
        "符號": _sign6[i],
        "本爻支": result["本卦"]!.layout[i],
        "變爻支": result["變卦"]!.layout[i],
        "伏爻支": result["伏卦"]!.layout[i],
      };
      yao["本爻親"] = getQin(yao["本爻支"]);
      yao["變爻親"] = getQin(yao["變爻支"]);
      yao["伏爻親"] = getQin(yao["伏爻支"]);
      qinCollect.add(yao["本爻親"]);
      yaoList.add(yao);
    }

    for (var yao in yaoList) {
      // 移除無用的伏爻
      if (qinCollect.contains(yao["伏爻親"])) {
        yao["伏爻支"] = "";
        yao["伏爻親"] = "";
      }
      // 移除無用的變爻
      if (yao["符號"] == "-" || yao["符號"] == "=") {
        yao["變爻支"] = "";
        yao["變爻親"] = "";
      }
    }

    result["爻"] = yaoList;
    result["空亡"] = d.xunkong;
    // 月沖為破
    result["月破"] = Zhi.byIndex(m.zhi.index + 6).name;
    // 日支沖的地支，旺相為日動，休囚為日破
    final anZhi = Zhi.byIndex(d.zhi.index + 6);
    final anSk = ShengKe.byCompare(m.zhi, anZhi);
    result["暗動"] = anZhi.name + (anSk.score < 2 ? "日動" : "日破");

    result['神煞'] = Gods.getList(d);

    /// 卦身爻
    // 陰世則從午月起，陽世還從子月生；欲得識其卦中意，從初數到世方真。
    var yy = orginal[shiIndex] == '-' ? 0 : 6;
    result["神煞"]["身爻"] = Zhi.byIndex(yy + shiIndex).name;

    return result;
  }
}
