import 'package:adv_yijing/pages/divine_edit/pickers/picker_controller.dart';
import 'package:get_storage/get_storage.dart';
import 'package:wu_llm/wu_llm.dart';
export 'package:wu_llm/wu_llm.dart';

class LlmController extends GetxController {
  final serviceList = ["Gemini", "Groq", "ChatGPT"];
  String serviceName = "";
  String apiKey = "";
  String modelName = "";
  List<String> modelList = [];

  LlmController() {
    serviceName = serviceList.first;
    loadSettings();
  }

  LlmBase getService() {
    return LlmBase.getService(serviceName);
  }

  saveSettings() async {
    final storage = GetStorage();
    await storage.write("serviceName", serviceName);
    await storage.write("${serviceName}_apiKey", apiKey);
    await storage.write("${serviceName}_modelName", modelName);
    loadModelList();
  }

  loadSettings({String? newServiceName}) async {
    final storage = GetStorage();
    if (newServiceName != null) {
      await storage.write("serviceName", newServiceName);
    }
    serviceName = newServiceName ?? storage.read("serviceName") ?? serviceList.first;
    apiKey = storage.read("${serviceName}_apiKey") ?? "";
    modelName = storage.read("${serviceName}_modelName") ?? "";
    loadModelList();
  }

  loadModelList() async {
    final service = getService();
    final colorScheme = Get.theme.colorScheme;
    service.getModels(apiKey).then((value) {
      modelList = value;
      modelList.sort();
      Get.snackbar(
        "載入模型",
        "成功載入 ${modelList.length} 個模型",
        backgroundColor: colorScheme.secondaryContainer,
        colorText: colorScheme.onSecondaryContainer,
      );
      update();
    }).catchError((error) {
      modelList = [];
      Get.snackbar(
        "載入模型列表失敗",
        "${service.serviceName} 載入模型列表失敗",
        backgroundColor: colorScheme.errorContainer,
        colorText: colorScheme.onErrorContainer,
      );
      update();
    });
  }
}
