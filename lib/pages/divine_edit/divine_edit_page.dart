import 'package:adv_yijing/pages/divine_view/divine_view_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:wu_fortune/wu_calendar.dart';

import '../../models/yijing_model.dart';
import 'divine_field.dart';
import 'gzdate_field.dart';
import 'solar_field.dart';

class DivineEditPage extends StatefulWidget {
  final YijingModel? model;
  const DivineEditPage({super.key, this.model});

  @override
  State<DivineEditPage> createState() => _DivineEditPageState();
}

class _DivineEditPageState extends State<DivineEditPage> {
  final _formKey = GlobalKey<FormBuilderState>();
  final questionCtrl = TextEditingController();
  final solarCtrl = TextEditingController();
  final gzdateCtrl = TextEditingController();
  YijingModel model = YijingModel();

  @override
  void initState() {
    if (widget.model != null) model = widget.model!;
    questionCtrl.text = model.question;
    solarCtrl.text = model.divineAtSolar?.toString() ?? '';
    gzdateCtrl.text = model.divineAtGzDate ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('占卜'),
        actions: [
          TextButton(
              style: TextButton.styleFrom(
                disabledForegroundColor: Colors.grey,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              child: Text("儲存"),
              onPressed: () {
                // print("validate: ${_formKey.currentState?.validate()}");
                if ((_formKey.currentState?.validate() ?? false) == false) return;
                _formKey.currentState?.save();
                model.save();
                Get.off(() => DivineViewPage(model: model));
              }),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 8, right: 8, top: 8),
            child: _buildForm(),
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    return FormBuilder(
      key: _formKey,
      child: Column(
        spacing: 8,
        children: [
          // 問事者資訊
          _buildGender(),
          // 占問內容
          _buildQuestion(),
          // 取卦陽曆
          _buildDivineSolar(),
          // 取卦干支曆（若有陽曆則內容為陽曆轉換且不能修改）
          _buildDivineGzDate(),
          // 取卦
          _buildRoll(),
        ],
      ),
    );
  }

  Widget _buildGender() {
    final items = ['男', '女', '不明'];
    return FormBuilderChoiceChips(
      name: "問卦性別",
      initialValue: model.gender,
      options: items.map((e) => FormBuilderChipOption(value: e, child: Text(e))).toList(),
      decoration: const InputDecoration(
        labelText: "問卦性別",
      ),
      onSaved: (newValue) {
        model.gender = newValue ?? '';
      },
    );
  }

  Widget _buildQuestion() {
    return FormBuilderTextField(
      name: "問題",
      controller: questionCtrl,
      decoration: const InputDecoration(
        labelText: "問題",
      ),
      minLines: 3,
      maxLines: 5,
      validator: FormBuilderValidators.required(),
      onSaved: (newValue) {
        model.question = newValue ?? '';
      },
    );
  }

  Widget _buildDivineSolar() {
    return SolarField(
      decoration: InputDecoration(
        labelText: "陽曆日期",
      ),
      controller: solarCtrl,
      onChanged: (newValue) {
        if (newValue.isEmpty) {
          gzdateCtrl.text = '';
          return;
        }
        final solar = DateTime.parse(newValue);
        final gzdate = GzDate.bySolar(solar);
        gzdateCtrl.text = gzdate.toString();
      },
      onSaved: (newValue) {
        model.divineAtSolar = newValue ?? '';
      },
    );
  }

  Widget _buildDivineGzDate() {
    return GzdateField(
      decoration: InputDecoration(
        labelText: "干支日期",
      ),
      controller: gzdateCtrl,
      validator: FormBuilderValidators.required(),
      onSaved: (newValue) {
        model.divineAtGzDate = newValue ?? '';
      },
    );
  }

  Widget _buildRoll() {
    return DivineField(
      decoration: const InputDecoration(
        labelText: "取卦",
      ),
      onSaved: (newValue) {
        model.pickerData = newValue;
      },
    );
  }
}
