import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../controllers/llm_controller.dart';

class LlmSettingTile extends StatefulWidget {
  const LlmSettingTile({super.key});

  @override
  State<LlmSettingTile> createState() => _LlmSettingTileState();
}

class _LlmSettingTileState extends State<LlmSettingTile> {
  final apikeyCtrl = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 8,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildServiceRow(),
        buildApiUrlRow(),
        buildApiKeyRow(),
        buildModelRow(),
      ],
    );
  }

  Widget buildServiceRow() {
    return GetBuilder<LlmController>(builder: (llmCtrl) {
      return FormBuilderDropdown(
        name: "llmService",
        initialValue: llmCtrl.serviceName,
        items: llmCtrl.serviceList.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
        onChanged: (value) async {
          llmCtrl.loadSettings(newServiceName: value ?? llmCtrl.serviceList.first);
        },
      );
    });
  }

  Widget buildApiUrlRow() {
    return GetBuilder<LlmController>(builder: (llmCtrl) {
      return Column(
        children: [
          TextButton(
              onPressed: () {
                launchUrl(Uri.parse(llmCtrl.getService().apiKeyUrl));
              },
              child: Text("申請 API 金鑰：${llmCtrl.getService().apiKeyUrl}")),
          Text("使用自己的金鑰可以不用受限於服務商的呼叫次數限制。", style: TextStyle(color: Colors.grey)),
        ],
      );
    });
  }

  Widget buildApiKeyRow() {
    return GetBuilder<LlmController>(builder: (llmCtrl) {
      apikeyCtrl.text = llmCtrl.apiKey;
      return FormBuilderTextField(
        name: "llmApiKey",
        controller: apikeyCtrl,
        decoration: InputDecoration(
          labelText: "API 金鑰",
          suffix: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 清除按鈕
              IconButton(
                onPressed: () => apikeyCtrl.clear(),
                icon: Icon(Icons.clear),
              ),
              // 刷新按鈕
              IconButton(
                  onPressed: () {
                    llmCtrl.apiKey = apikeyCtrl.text;
                    llmCtrl.loadModelList();
                  },
                  icon: Icon(Icons.refresh)),
            ],
          ),
        ),
        onChanged: (value) {
          llmCtrl.apiKey = value ?? "";
          llmCtrl.saveSettings();
        },
      );
    });
  }

  Widget buildModelRow() {
    return GetBuilder<LlmController>(builder: (llmCtrl) {
      return FormBuilderDropdown(
        name: "llmModel",
        initialValue: llmCtrl.modelList.contains(llmCtrl.modelName) ? llmCtrl.modelName : null,
        items: llmCtrl.modelList.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
        onChanged: (value) {
          llmCtrl.modelName = value ?? "";
          llmCtrl.saveSettings();
        },
      );
    });
  }
}
