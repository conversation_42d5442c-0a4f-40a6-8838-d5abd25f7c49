// ignore_for_file: unintended_html_in_doc_comment

import 'dart:developer';

import 'package:wu_fortune/wu_calendar.dart';

import '../models/yijing_model.dart';

class LlmPrompts {
  final YijingModel model;
  late Yijing yijing;
  late Map<String, dynamic> gzDict;
  LlmPrompts(this.model) {
    yijing = Yijing(model.pickerData?.sign6 ?? '');
    gzDict = GzDate.toDict(model.divineAtGzDate ?? '');
  }

  (String systemPrompt, String userPrompt) getPrompt(ReadingMode mode) {
    return switch (mode) {
      ReadingMode.gaodao => getGaodaoPrompt(),
      ReadingMode.tiyong => getTiyongPrompt(),
      ReadingMode.liuyao => getLiuyaoPrompt(),
      ReadingMode.yijing => getYijingPrompt(),
    };
  }

  /// 高島吞象
  (String systemPrompt, String userPrompt) getGaodaoPrompt() {
    String sysPrompt = """
你是一位易經老師，用簡單易懂的方式解讀卦象。請直接告訴我結果，不要分析過程。

請用這樣的方式回答：
1. **現在的狀況**：本卦告訴我什麼
2. **需要注意的變化**：變爻提醒我什麼
3. **最終結果**：變卦顯示會怎樣
4. **實際建議**：我該怎麼做

重點：
- 回答要口語化，像朋友聊天一樣，直接說重點就好。
- 不要有前綴的描述或提示詞。

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 現在的狀況
## 需要注意的變化
## 最終結果
## 實際建議
```
    """;
    String userPrompt = """
請為下列內容解讀卦象：
**問卦性別：** ${model.gender}
**占卦時間：** ${model.divineAtGzDate}
**問題：** ${model.question}
**本卦：** ${Gua64.bySign6(yijing.orginal).fullname}
**變卦：** ${Gua64.bySign6(yijing.changed).fullname}
**變爻：** ${yijing.dongYao}
""";
    log(userPrompt);
    return (sysPrompt, userPrompt);
  }

  /// 體用梅花易數
  (String systemPrompt, String userPrompt) getTiyongPrompt() {
    String sysPrompt = """
你是梅花易數專家，用簡單的話解讀卦象。直接說結果就好，不要分析過程。

重點：
- 體卦是你自己，用卦是你問的事
- 看體用關係就知道結果：用剋體=不利，體生用=要努力，用生體=順利
- 回答要口語化，像朋友聊天一樣，直接說重點就好。
- 不要有前綴的描述或提示詞。

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 你的狀況 (體卦)
直接說這代表什麼

## 事情的狀況 (用卦)
直接說這代表什麼，還有方位提示

## 體用關係
直接說對你有利還是不利

## 現在的情況
直接描述目前狀況

## 變化的關鍵
直接說什麼會改變

## 最終結果
直接說會變成怎樣

## 建議
簡單說該怎麼做
```
""";

    final layout = yijing.getMeiyiLayout(GanZhi.byName(gzDict["月"]));
    String userPrompt = """
* **問卦性別：** ${model.gender}
* **占卦時間：** ${model.divineAtGzDate}
* **問題：** ${model.question}
* **本卦：** ${layout["八卦"][0].fullname}
* **互卦：** ${layout["八卦"][1].fullname}
* **變卦：** ${layout["八卦"][2].fullname}
## 單卦從體卦開始
""";
    for (var index = 0; index < 5; index++) {
      final gua = layout["單卦"][index];
      userPrompt += "### 卦名：${gua["卦名"]}\n";
      userPrompt += "- 五行：${gua["五行"]}\n";
      if (index == 0) continue;
      userPrompt += "- 生剋：${gua["生剋"]}\n";
      userPrompt += "- 旺衰：${gua["旺衰"]}\n";
      userPrompt += "- 吉凶：${gua["吉凶"]}\n";
      userPrompt += "- 分數：${gua["分數"]}\n";
    }
    log(userPrompt);
    return (sysPrompt, userPrompt);
  }

  /// 易經解讀
  (String systemPrompt, String userPrompt) getYijingPrompt() {
    String sysPrompt = """
你是易經老師，用白話文解讀卦象。直接說結果，不要分析過程。

簡單說明：
- 本卦：現在的狀況
- 變爻：關鍵變化
- 變卦：最終結果
- 互卦：內在因素
- 綜卦：另一個角度
- 錯卦：相反的可能

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 現在的狀況 (本卦) [卦名]
直接說目前是什麼情況

## 關鍵變化 (變爻) [第幾爻]
直接說什麼會改變

## 最終結果 (變卦) [卦名]
直接說會變成怎樣

## 內在因素 (互卦) [卦名]
直接說隱藏的影響

## 另一個角度 (綜卦) [卦名]
從完全相反的角度看事情，揭示潛在的對立面或互補關係，思考如果所有條件都翻轉會是什麼結果。

## 相反可能 (錯卦) [卦名]
從另一個立場或顛倒的視角看問題，揭示事物內部不同面向的關係，思考如果換個角度或換個人來看會發現什麼。

## 實用建議
簡單說該怎麼做
```
""";
    String userPrompt = """
* **占卦時間：** ${model.divineAtGzDate}
* **問題：** ${model.question}
* **本卦：** ${Gua64.bySign6(yijing.orginal).fullname}
* **互卦：** ${Gua64.bySign6(yijing.interaction).fullname}
* **變卦：** ${Gua64.bySign6(yijing.changed).fullname}
* **錯卦：** ${Gua64.bySign6(yijing.swap).fullname}
* **綜卦：** ${Gua64.bySign6(yijing.reverse).fullname}
* **變爻：** ${yijing.dongYao}
""";
    log(userPrompt);
    return (sysPrompt, userPrompt);
  }

  /// 六爻解讀
  (String systemPrompt, String userPrompt) getLiuyaoPrompt() {
    String sysPrompt = """
你是六爻專家，用簡單的話解讀卦象。直接說結果，不要分析過程。

重點：
- 用神是關鍵，看它旺不旺
- 世爻是你，應爻是對方或事情
- 直接說吉凶，不要解釋原理

格式：
```markdown
## 摘要
用30字以內總結卦象的狀況

## 用神狀況
直接說用神旺不旺，對你有利還是不利

## 世應關係
直接說你和對方/事情的關係如何

## 重要變化
直接說哪些爻在動，會帶來什麼影響

## 吉凶判斷
直接說結果是好是壞

## 時間預測
如果能判斷，直接說大概什麼時候

## 實用建議
簡單說該怎麼做
```
""";
    final layout = yijing.getLiuyaoLayout(GanZhi.byName(gzDict["月"]), GanZhi.byName(gzDict["日"]));
    String userPrompt = """
* 占卦時間： ${model.divineAtGzDate}
* 問題： ${model.question}
* 本卦： ${layout["本卦"].fullname}
* 變卦： ${layout["變卦"].fullname}
* 旬空： ${layout["空亡"]}
* 月破： ${layout["月破"]}
* 暗動： ${layout["暗動"]}
* 世爻： ${layout["世爻"]}
* 應爻： ${layout["應爻"]}
## 爻位從上至下
""";
    final yaoList = layout["爻"] as List<Map<String, dynamic>>;
    for (var yaoIndex = 0; yaoIndex < yaoList.length; yaoIndex++) {
      final yao = yaoList[yaoIndex];
      userPrompt += "\n### ${yao["爻名"]}爻\n";
      userPrompt += "- 六獸：${yao["六獸"]}\n";
      userPrompt += "- 符號：${yao["符號"]}\n";
      userPrompt += "- 本爻：${yao["本爻支"]}${yao["本爻親"]}\n";
      userPrompt += "- 變爻：${yao["變爻支"]}${yao["變爻親"]}\n";
      userPrompt += "- 伏爻：${yao["伏爻支"]}${yao["伏爻親"]}\n";
    }

    final godList = layout["神煞"] as Map<String, String>;
    userPrompt += "\n## 神煞\n";
    for (var god in godList.entries) {
      userPrompt += "- ${god.key}：${god.value}\n";
    }
    log(userPrompt);
    return (sysPrompt, userPrompt);
  }

  /// 取出 摘要 內容
  static String getSummary(String response) {
    final pattern = RegExp(r'## 摘要\n([\s\S]*?)(?=\n##|\Z)'); // 捕獲摘要內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 取出 markdown 內容
  static String getMarkdownInner(String response) {
    final pattern = RegExp(r'```markdown\n([\s\S]*?)```'); // 捕獲 markdown 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 取出 JSON 內容
  static String getJsonInner(String response) {
    final pattern = RegExp(r'```json\n([\s\S]*?)```'); // 捕獲 JSON 內容
    final match = pattern.firstMatch(response); // 尋找匹配的部分
    return match?.group(1) ?? response; // 取出群組 1 或原始字串
  }

  /// 移除<think></think>之間的內容
  static String removeThink(String response) {
    return response.replaceAll(RegExp(r'<think>[\s\S]*?</think>'), '');
  }
}
