import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_base.dart';

/// Ollama 服務實現
class LlmOllama extends LlmBase {
  static const String _baseUrl = 'http://localhost:11434/api';

  LlmOllama()
      : super(
          serviceName: 'Ollama',
          recommandModels: [],
          apiKeyUrl: 'https://ollama.com/download',
        );

  @override
  Future<List<String>> getModels(String apiKey) async {
    final dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json'},
      ),
    );

    final response = await dio.get('/tags');
    final data = response.data as Map<String, dynamic>;
    final models = (data['models'] as List).map((model) => model['name'] as String).toList();
    return models;
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
        LlmRole.system => 'system',
        LlmRole.user => 'user',
        LlmRole.assistant => 'assistant',
      };

  @override
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      final response = await dio.post(
        '/chat',
        data: {'model': modelName, 'messages': formatMessages(messages), 'stream': false},
      );

      final result = response.data['message']['content'];
      return result ?? '';
    } catch (e) {
      return handleError(e);
    }
  }

  @override
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async* {
    try {
      final dio = Dio(
        BaseOptions(
          baseUrl: _baseUrl,
          sendTimeout: sendTimeout,
          receiveTimeout: receiveTimeout,
          headers: {'Content-Type': 'application/json'},
        ),
      );

      // Implement streaming similar to other models
      // ...
    } catch (e) {
      yield handleError(e);
    }
  }
}
