import 'package:dio/dio.dart';

import '../wu_llm.dart';

/// LLM 服務基礎抽象類
abstract class LlmBase {
  static LlmBase getService(String serviceName) {
    switch (serviceName) {
      case "ChatGPT":
        return LlmChatGpt();
      case "Gemini":
        return LlmGemini();
      case "Claude":
        return LlmClaude();
      case "Mistral":
        return LlmMistral();
      case "Groq":
        return LlmGroq();
      case "Perplexity":
        return LlmPerplexity();
      case "Deepseek":
        return LlmDeepseek();
      default:
        throw Exception("不支援的 LLM 服務：$serviceName");
    }
  }

  /// LLM 服務名稱
  final String serviceName;

  /// API 金鑰申請網址
  final String apiKeyUrl;

  /// 預設模型名稱
  final List<String> recommandModels;

  /// Dio 實例
  Dio? dio;

  /// 發送超時時間
  Duration sendTimeout = const Duration(seconds: 30);

  /// 接收超時時間
  Duration receiveTimeout = const Duration(minutes: 1);

  /// 建構函數
  LlmBase({
    required this.serviceName,
    required this.apiKeyUrl,
    this.recommandModels = const [],
  });

  /// 獲取可用模型列表
  Future<List<String>> getModels(String apiKey);

  /// 流式文本生成
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  });

  /// 非流式文本生成
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  });

  /// 將角色轉換為服務特定的字串格式
  String roleCast(LlmRole role) => role.toString().split('.')[1];

  /// 將訊息列表轉換為服務特定的格式
  List<Map<String, dynamic>> formatMessages(List<LlmMessage> messages) {
    final result = messages.map((msg) => {'role': roleCast(msg.role), 'content': msg.content}).toList();
    return result;
  }

  /// 錯誤處理方法
  String handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
          return '連接超時';
        case DioExceptionType.sendTimeout:
          return '發送請求超時';
        case DioExceptionType.receiveTimeout:
          return '接收響應超時';
        case DioExceptionType.badResponse:
          return '服務器響應錯誤：${error.response?.statusCode}';
        default:
          return '網絡錯誤：${error.message}';
      }
    }
    return '未知錯誤：$error';
  }
}
