import 'dart:convert';

import 'package:dio/dio.dart';
import 'llm_message.dart';
import 'llm_base.dart';

/// Gemini 服務實現
class LlmGemini extends LlmBase {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1';

  LlmGemini()
      : super(
          serviceName: 'Gemini',
          apiKeyUrl: 'https://aistudio.google.com/app/apikey',
        );

  @override
  Future<List<String>> getModels(String apiKey) async {
    final dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json'},
        queryParameters: {'key': apiKey},
      ),
    );

    final response = await dio.get('/models');
    final data = response.data as Map<String, dynamic>;
    final models = (data['models'] as List).map((model) => model['name'] as String).toList();
    for (var i = 0; i < models.length; i++) {
      if (models[i].startsWith('models/')) {
        models[i] = models[i].substring(7);
      }
    }
    return models;
  }

  @override
  String roleCast(LlmRole role) => switch (role) {
        LlmRole.system => 'user',
        LlmRole.user => 'user',
        LlmRole.assistant => 'model',
      };

  @override
  List<Map<String, dynamic>> formatMessages(List<LlmMessage> messages) {
    final result = messages
        .map((msg) => {
              'role': roleCast(msg.role),
              'parts': [
                {'text': msg.content}
              ]
            })
        .toList();
    return result;
  }

  @override
  Future<String?> futureText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async {
    final dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json'},
        queryParameters: {'key': apiKey},
      ),
    );
    // 使用 OpenAI 相容的 API 端點
    final response = await dio.post(
      '/models/$modelName:generateContent',
      data: {
        'contents': formatMessages(messages),
        'generationConfig': {'temperature': 0.7, 'topK': 40, 'topP': 0.95, 'maxOutputTokens': 2048},
      },
    );

    final result = response.data['candidates'][0]['content']['parts'][0]['text'];
    return result ?? '';
  }

  @override
  Stream<String?> streamText({
    required String apiKey,
    required String modelName,
    required List<LlmMessage> messages,
  }) async* {
    final dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        headers: {'Content-Type': 'application/json'},
        queryParameters: {'key': apiKey},
      ),
    );
    final response = await dio.post<ResponseBody>(
      '/models/$modelName:streamGenerateContent',
      data: formatMessages(messages),
      options: Options(
        responseType: ResponseType.stream, // 關鍵：將響應類型設定為串流
      ),
    );
    await for (final data in response.data!.stream) {
      final chunk = utf8.decode(data); // 將 byte 數據解碼為字串
      // Gemini API 返回的每個事件都是一個完整的 JSON 對象，後跟一個換行符。
      // 然而，一個 TCP 塊可能包含多個或部分的 JSON 對象。
      // 最穩健的方式是緩衝並逐行解析。

      // 通常 Gemini 的串流格式是每行一個 JSON 對象
      // 範例:
      // {"candidates":[{"content":{"parts":[{"text":"你好"}]}}]}
      // {"candidates":[{"content":{"parts":[{"text":"嗎？"}]}}]}

      // 這裡我們直接假設每個 'data' chunk 都是可解析的 JSON 片段，
      // 但實際情況下，一個 Dio 'data' chunk 可能包含多個事件，
      // 或者一個事件被分割在多個 Dio 'data' chunk 中。
      // 因此，需要一個更複雜的解析器，類似 SSE 的事件解析器。

      // 簡單的處理方式：嘗試解析每一個 chunk
      // 由於 Gemini API 通常在每個事件後提供換行符，我們嘗試按行分割。
      // 但一個 'data' chunk 可能包含多個完整的 JSON 對象或部分 JSON。
      // 最安全的方式是累積 buffer 並在遇到換行符時解析。
      // 為簡化起見，這裡直接嘗試解析收到的塊。
      // 實際應用中，如果遇到解析錯誤，需要更精細的緩衝和分割邏輯。

      // 為了處理 Gemini 串流響應的特殊性 (可能是多個 JSON 物件在一個 chunk，
      // 或是部分 JSON 物件，通常以換行符分隔)，我們需要更 robust 的解析。
      // 簡單的做法是假設每個 'data' chunk 已經是完整的 JSON 字符串，
      // 或者至少是能被逐步解析的。
      // Gemini API 的 streaming responses 的格式通常是 newline-delimited JSON.

      final lines = chunk.split('\n'); // 嘗試按行分割
      for (final line in lines) {
        if (line.trim().isEmpty) continue;
        try {
          final jsonResponse = jsonDecode(line);
          if (jsonResponse is Map<String, dynamic> && jsonResponse.containsKey('candidates')) {
            final candidates = jsonResponse['candidates'] as List;
            if (candidates.isNotEmpty) {
              final content = candidates[0]['content'];
              if (content != null && content.containsKey('parts')) {
                final parts = content['parts'] as List;
                if (parts.isNotEmpty) {
                  final text = parts[0]['text'] as String?;
                  if (text != null && text.isNotEmpty) {
                    yield text; // 每次產生一個文字片段
                  }
                }
              }
            }
          }
        } catch (e) {
          // 如果解析失敗，這可能表示這個 chunk 只是部分 JSON
          // 在生產環境中，您可能需要一個更複雜的緩衝機制來處理這種情況。
          // 例如，將不完整的行加入一個緩衝區，等待下一個 chunk 來補齊。
          print('Failed to parse JSON chunk: $line, Error: $e');
          // 可以選擇 yield 錯誤信息或者忽略
        }
      }
    }
  }
}
